#!/usr/bin/env python3
"""
Sentinel-2 TIFF Pixel and Band Analyzer
Analyzes your 2025-05-05-00_00_2025-05-05-23_59_Sentinel-2_L2A_False_color.tiff file
"""

import numpy as np
from osgeo import gdal
import os
import sys

def analyze_sentinel2_tiff(filepath):
    """Analyze Sentinel-2 TIFF file and extract pixel/band information"""
    
    print(f"🔍 Analyzing: {os.path.basename(filepath)}")
    print("=" * 60)
    
    # Open the TIFF file
    try:
        dataset = gdal.Open(filepath)
        if dataset is None:
            print(f"❌ ERROR: Could not open {filepath}")
            return
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return
    
    # Get basic info
    width = dataset.RasterXSize
    height = dataset.RasterYSize
    bands = dataset.RasterCount
    
    print(f"📐 Dimensions: {width} x {height} pixels")
    print(f"📊 Number of bands: {bands}")
    print(f"🔢 Total pixels: {width * height:,}")
    
    # Get geospatial info
    geotransform = dataset.GetGeoTransform()
    projection = dataset.GetProjection()
    
    if geotransform:
        print(f"🌍 Pixel size: {geotransform[1]:.6f} x {abs(geotransform[5]):.6f}")
        print(f"📍 Top-left corner: ({geotransform[0]:.6f}, {geotransform[3]:.6f})")
    
    # Analyze each band
    print(f"\n🎨 BAND ANALYSIS:")
    print("-" * 40)
    
    band_names = ["Near Infrared (NIR)", "Red", "Green"]
    band_descriptions = [
        "NIR - Vegetation appears bright",
        "Red - Soil and rock features", 
        "Green - Water and vegetation"
    ]
    
    all_band_data = []
    
    for i in range(1, bands + 1):
        band = dataset.GetRasterBand(i)
        band_name = band_names[i-1] if i-1 < len(band_names) else f"Band {i}"
        
        print(f"\n🎯 {band_name}:")
        
        # Get data type
        data_type = gdal.GetDataTypeName(band.DataType)
        print(f"   Data type: {data_type}")
        
        # Get nodata value
        nodata = band.GetNoDataValue()
        print(f"   NoData value: {nodata}")
        
        # Read the entire band
        print("   📖 Reading pixel data...")
        band_array = band.ReadAsArray()
        all_band_data.append(band_array)
        
        # Calculate statistics (excluding nodata)
        if nodata is not None:
            valid_pixels = band_array[band_array != nodata]
        else:
            valid_pixels = band_array[band_array > 0]  # Assume 0 is nodata
        
        if len(valid_pixels) > 0:
            min_val = np.min(valid_pixels)
            max_val = np.max(valid_pixels)
            mean_val = np.mean(valid_pixels)
            std_val = np.std(valid_pixels)
            
            print(f"   📈 Min value: {min_val}")
            print(f"   📈 Max value: {max_val}")
            print(f"   📈 Mean value: {mean_val:.2f}")
            print(f"   📈 Std deviation: {std_val:.2f}")
            print(f"   ✅ Valid pixels: {len(valid_pixels):,}")
            
            # Show sample pixel values
            sample_pixels = valid_pixels[:20]
            print(f"   🔍 Sample values: {sample_pixels}")
        else:
            print("   ⚠️ No valid pixels found!")
    
    # Spatial sampling from different regions
    print(f"\n📍 SPATIAL SAMPLING (5x5 pixel areas):")
    print("-" * 40)
    
    regions = {
        "Top-Left": (0, 0),
        "Top-Right": (width-5, 0),
        "Center": (width//2-2, height//2-2),
        "Bottom-Left": (0, height-5),
        "Bottom-Right": (width-5, height-5)
    }
    
    for region_name, (start_x, start_y) in regions.items():
        print(f"\n🎯 {region_name} region:")
        
        for band_idx, band_array in enumerate(all_band_data):
            band_name = band_names[band_idx] if band_idx < len(band_names) else f"Band {band_idx+1}"
            
            # Extract 5x5 sample
            sample = band_array[start_y:start_y+5, start_x:start_x+5]
            
            if sample.size > 0:
                avg_val = np.mean(sample)
                min_val = np.min(sample)
                max_val = np.max(sample)
                print(f"   {band_name}: avg={avg_val:.1f}, range=[{min_val}-{max_val}]")
    
    # Overall statistics
    print(f"\n📊 OVERALL STATISTICS:")
    print("-" * 40)
    
    if all_band_data:
        all_data = np.stack(all_band_data)
        overall_min = np.min(all_data[all_data > 0])
        overall_max = np.max(all_data)
        overall_mean = np.mean(all_data[all_data > 0])
        
        print(f"🔢 Overall min value: {overall_min}")
        print(f"🔢 Overall max value: {overall_max}")
        print(f"🔢 Overall mean value: {overall_mean:.2f}")
        print(f"🎨 Dynamic range: {overall_max - overall_min}")
    
    print(f"\n✅ Analysis complete!")
    
    # Close dataset
    dataset = None

if __name__ == "__main__":
    # Look for your specific file
    filename = "2025-05-05-00_00_2025-05-05-23_59_Sentinel-2_L2A_False_color.tiff"
    
    if os.path.exists(filename):
        analyze_sentinel2_tiff(filename)
    else:
        print(f"❌ File not found: {filename}")
        print("📁 Current directory contents:")
        for f in os.listdir("."):
            if f.endswith((".tiff", ".tif", ".TIF", ".TIFF")):
                print(f"   📄 {f}")
        
        print("\n💡 Usage: python analyze_sentinel2.py")
        print("   Make sure your TIFF file is in the same directory as this script")
